# Automated Betting System for owo Discord Bot

An automated betting system that places bets on the owo Discord gambling bot using HTTP requests.

## Features

- **Multiple Betting Strategies**:
  - Fixed Amount: Bet the same amount every time
  - Martingale: Double bet after losses, reset after wins
  - Extensible framework for adding more strategies

- **Risk Management**:
  - Configurable stop-loss limits
  - Maximum consecutive loss protection
  - Bankroll management
  - Rate limiting to avoid Discord API limits

- **Tracking & Statistics**:
  - Comprehensive bet history tracking
  - Real-time profit/loss calculations
  - Win rate and ROI statistics
  - Persistent data storage

## Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Discord credentials**:
   - Copy `.env.example` to `.env`
   - Update with your Discord token and channel ID
   - Or modify `config.py` directly

3. **Run the bot**:
   ```bash
   python main.py
   ```

## Usage

The bot provides an interactive menu with the following options:

1. **Setup Fixed Amount Strategy**: Bet the same amount every time
2. **Setup Martingale Strategy**: Progressive betting system
3. **Place Single Bet**: Test with one bet
4. **Run Continuous Betting**: Automated betting loop
5. **Show Statistics**: View current performance
6. **Exit**: Stop the bot

## Configuration

Edit `config.py` to customize:

- **Betting limits**: Min/max bet amounts
- **Risk management**: Stop-loss percentages, max consecutive losses
- **Rate limiting**: Minimum time between bets
- **Strategy parameters**: Martingale multiplier, etc.

## Current Limitations

⚠️ **Message fetching not implemented yet**

The system can place bets but cannot automatically read owo bot responses to determine win/loss results. This means:

- Bet results need to be manually tracked for now
- Strategy adjustments based on results won't work automatically
- Statistics will only show bets placed, not outcomes

## Next Steps

1. **Implement message fetching** to read owo bot responses
2. **Add response parsing** to extract win/loss information
3. **Enable automatic result tracking**
4. **Add more sophisticated strategies** (arbitrage, etc.)

## Safety Notes

- Start with small amounts to test
- Monitor the bot closely
- Set appropriate stop-loss limits
- Be aware of Discord rate limits
- Gambling involves risk - bet responsibly

## File Structure

```
├── main.py                 # Main application
├── discord_client.py       # Discord HTTP client
├── bet_tracker.py         # Bet history tracking
├── config.py              # Configuration
├── betting_strategies/    # Strategy implementations
│   ├── base_strategy.py   # Abstract base class
│   ├── fixed_amount.py    # Fixed amount strategy
│   └── martingale.py      # Martingale strategy
├── requirements.txt       # Python dependencies
└── README.md             # This file
```
