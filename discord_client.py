import requests
import json
import time
import random
from typing import Optional, Dict, Any
from config import Config

class DiscordClient:
    def __init__(self):
        self.config = Config()
        self.session = requests.Session()
        self.session.headers.update(self.config.HEADERS)
        self.last_request_time = 0
        
    def _generate_nonce(self) -> str:
        """Generate a unique nonce for Discord messages"""
        return str(int(time.time() * 1000000) + random.randint(1000, 9999))
    
    def _rate_limit(self):
        """Ensure we don't send requests too quickly with random intervals"""
        import random

        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        # Use random interval between 9-14 seconds to avoid owo bot timeouts
        min_interval = random.uniform(self.config.MIN_BET_INTERVAL, self.config.MAX_BET_INTERVAL)

        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            print(f"⏰ Rate limiting: waiting {sleep_time:.1f} seconds to avoid owo timeout...")
            time.sleep(sleep_time)

        self.last_request_time = time.time()
    
    def send_message(self, content: str) -> Optional[Dict[Any, Any]]:
        """Send a message to the Discord channel"""
        self._rate_limit()
        
        url = f"https://discord.com/api/v9/channels/{self.config.CHANNEL_ID}/messages"
        
        payload = {
            "mobile_network_type": "unknown",
            "content": content,
            "nonce": self._generate_nonce(),
            "tts": False,
            "flags": 0
        }
        
        try:
            print(f"Sending message: {content}")
            response = self.session.post(url, json=payload)
            
            if response.status_code == 200:
                print("✅ Message sent successfully")
                return response.json()
            elif response.status_code == 429:
                # Rate limited
                retry_after = response.json().get('retry_after', 5)
                print(f"⚠️ Rate limited. Waiting {retry_after} seconds...")
                time.sleep(retry_after)
                return self.send_message(content)  # Retry
            else:
                print(f"❌ Failed to send message. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return None
    
    def place_bet(self, amount: int, side: Optional[str] = None) -> bool:
        """
        Place a coinflip bet
        
        Args:
            amount: Bet amount
            side: 'h' for heads, 't' for tails, None for random
        """
        if amount < self.config.MIN_BET_AMOUNT or amount > self.config.MAX_BET_AMOUNT:
            print(f"❌ Invalid bet amount: {amount}. Must be between {self.config.MIN_BET_AMOUNT} and {self.config.MAX_BET_AMOUNT}")
            return False
        
        # Build the command
        command = f"o cf {amount}"
        if side:
            command += f" {side}"
        
        result = self.send_message(command)
        return result is not None
    
    def get_recent_messages(self, limit: int = 50) -> Optional[list]:
        """
        Fetch recent messages from the channel
        """
        url = f"https://discord.com/api/v9/channels/{self.config.CHANNEL_ID}/messages"
        params = {'limit': limit}

        try:
            print(f"📥 Fetching {limit} recent messages...")
            response = self.session.get(url, params=params)

            if response.status_code == 200:
                messages = response.json()
                print(f"✅ Fetched {len(messages)} messages")
                return messages
            elif response.status_code == 429:
                # Rate limited
                retry_after = response.json().get('retry_after', 5)
                print(f"⚠️ Rate limited. Waiting {retry_after} seconds...")
                time.sleep(retry_after)
                return self.get_recent_messages(limit)  # Retry
            else:
                print(f"❌ Failed to fetch messages. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error fetching messages: {e}")
            return None

    def find_owo_response(self, messages: list, after_timestamp: float, username: str = "NG PENZ") -> Optional[Dict[Any, Any]]:
        """
        Find owo bot response after a specific timestamp for a specific user

        Args:
            messages: List of Discord messages
            after_timestamp: Only look for messages after this timestamp
            username: The username to look for in the owo response (default: "NG PENZ")
        """
        owo_bot_id = "408785106942164992"  # owo bot's user ID

        print(f"🔍 Looking for owo responses for user '{username}' after timestamp: {after_timestamp}")

        for message in messages:
            # Check if message is from owo bot
            if message.get('author', {}).get('id') != owo_bot_id:
                continue

            # Check if message is after our bet timestamp
            message_timestamp = self._parse_discord_timestamp(message.get('timestamp', ''))
            content = message.get('content', '')
            content_lower = content.lower()

            print(f"📝 OwO message at {message_timestamp}: {content[:50]}...")

            if message_timestamp <= after_timestamp:
                print(f"   ⏰ Too old: {message_timestamp} <= {after_timestamp}")
                continue

            # Check if message contains coinflip result AND is for the correct user
            if ('spent' in content_lower or 'chose' in content_lower) and username.upper() in content.upper():
                print(f"   ✅ Found coinflip response for {username}!")
                return message
            elif 'spent' in content_lower or 'chose' in content_lower:
                # Found a coinflip response but for a different user
                other_user = self._extract_username_from_message(content)
                print(f"   ❌ Found coinflip response but for different user: {other_user}")
                continue

        print(f"❌ No owo coinflip response found for {username} after {after_timestamp}")
        return None

    def _extract_username_from_message(self, content: str) -> str:
        """Extract username from owo bot message"""
        # owo format: **USERNAME** spent **amount** and chose **side**
        import re
        match = re.search(r'\*\*([^*]+)\*\* spent', content)
        if match:
            return match.group(1)
        return "Unknown"

    def _parse_discord_timestamp(self, timestamp_str: str) -> float:
        """Parse Discord timestamp to Unix timestamp"""
        try:
            from datetime import datetime
            # Discord timestamps are in ISO format: 2023-01-01T12:00:00.000000+00:00
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            return dt.timestamp()
        except:
            return 0.0
