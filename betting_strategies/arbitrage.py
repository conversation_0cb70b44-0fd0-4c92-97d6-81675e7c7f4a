import time
import random
from typing import Optional, Dict, Any
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult

class ArbitrageStrategy(BaseBettingStrategy):
    """
    Arbitrage strategy: Look for opportunities where we can guarantee profit
    
    For coinflip betting, true arbitrage is rare since it's usually 50/50 odds.
    This strategy focuses on:
    1. Pattern recognition from recent results
    2. Streak breaking (betting against long streaks)
    3. Bankroll management with calculated risks
    """
    
    def __init__(self, initial_bankroll: int, base_bet: int, min_edge: float = 0.02):
        super().__init__(initial_bankroll)
        self.base_bet = base_bet
        self.min_edge = min_edge  # Minimum edge required to place bet
        self.recent_results = []  # Track recent outcomes for pattern analysis
        self.max_recent_results = 20  # Keep last 20 results
        
        # Arbitrage parameters
        self.streak_threshold = 4  # Bet against streaks of 4+ same results
        self.pattern_confidence = 0.6  # Confidence threshold for patterns
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate next bet using arbitrage analysis"""
        if self.should_stop_betting():
            return None

        # If we don't have enough data, place some initial bets to gather data
        if len(self.recent_results) < 3:
            return BetInfo(
                amount=self.base_bet,
                side=random.choice(['h', 't']),
                timestamp=time.time()
            )

        # Analyze recent results for opportunities
        opportunity = self._analyze_arbitrage_opportunity()

        if not opportunity:
            # If no opportunity found but we have some data, occasionally place a small bet
            if len(self.recent_results) < 10 and random.random() < 0.3:  # 30% chance
                return BetInfo(
                    amount=self.base_bet // 2,  # Smaller bet for data gathering
                    side=random.choice(['h', 't']),
                    timestamp=time.time()
                )
            return None

        bet_amount = self._calculate_bet_size(opportunity['confidence'])
        side = opportunity['recommended_side']

        return BetInfo(
            amount=bet_amount,
            side=side,
            timestamp=time.time()
        )
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update arbitrage analysis with new result"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
        
        # Add result to recent results for pattern analysis
        result_data = {
            'won': won,
            'side_chosen': bet_info.side,
            'timestamp': bet_info.timestamp,
            'amount': bet_info.amount
        }
        
        self.recent_results.append(result_data)
        
        # Keep only recent results
        if len(self.recent_results) > self.max_recent_results:
            self.recent_results = self.recent_results[-self.max_recent_results:]
        
        print(f"🔍 Arbitrage: Updated with {'WIN' if won else 'LOSS'}, analyzing patterns...")
    
    def _analyze_arbitrage_opportunity(self) -> Optional[Dict[str, Any]]:
        """Analyze recent results for arbitrage opportunities"""
        if len(self.recent_results) < 3:
            return None
        
        opportunities = []
        
        # 1. Streak Analysis - bet against long streaks
        streak_opp = self._analyze_streaks()
        if streak_opp:
            opportunities.append(streak_opp)
        
        # 2. Pattern Analysis - look for repeating patterns
        pattern_opp = self._analyze_patterns()
        if pattern_opp:
            opportunities.append(pattern_opp)
        
        # 3. Mean Reversion - if too many losses, expect a win
        reversion_opp = self._analyze_mean_reversion()
        if reversion_opp:
            opportunities.append(reversion_opp)
        
        # Return the opportunity with highest confidence
        if opportunities:
            best_opp = max(opportunities, key=lambda x: x['confidence'])
            if best_opp['confidence'] >= self.min_edge:
                return best_opp
        
        return None
    
    def _analyze_streaks(self) -> Optional[Dict[str, Any]]:
        """Look for long streaks to bet against"""
        if len(self.recent_results) < self.streak_threshold:
            return None
        
        # Check for losing streaks
        recent_losses = 0
        for result in reversed(self.recent_results):
            if not result['won']:
                recent_losses += 1
            else:
                break
        
        if recent_losses >= self.streak_threshold:
            confidence = min(0.8, 0.4 + (recent_losses - self.streak_threshold) * 0.1)
            return {
                'type': 'streak_breaking',
                'confidence': confidence,
                'recommended_side': random.choice(['h', 't']),  # Random side, betting on streak break
                'reason': f'Breaking losing streak of {recent_losses}'
            }
        
        return None
    
    def _analyze_patterns(self) -> Optional[Dict[str, Any]]:
        """Look for repeating patterns in results"""
        if len(self.recent_results) < 6:
            return None
        
        # Simple pattern: if last 3 results were all losses, bet for a win
        last_3 = self.recent_results[-3:]
        if all(not r['won'] for r in last_3):
            return {
                'type': 'pattern_reversal',
                'confidence': 0.3,
                'recommended_side': random.choice(['h', 't']),
                'reason': 'Pattern suggests reversal after 3 losses'
            }
        
        return None
    
    def _analyze_mean_reversion(self) -> Optional[Dict[str, Any]]:
        """Analyze if results are deviating too much from expected mean"""
        if len(self.recent_results) < 10:
            return None
        
        # Calculate win rate in recent results
        recent_wins = sum(1 for r in self.recent_results if r['won'])
        win_rate = recent_wins / len(self.recent_results)
        
        # If win rate is very low, expect reversion to mean (50%)
        if win_rate < 0.2:  # Less than 20% wins
            confidence = 0.4 + (0.2 - win_rate) * 2  # Higher confidence for lower win rates
            return {
                'type': 'mean_reversion',
                'confidence': min(confidence, 0.7),
                'recommended_side': random.choice(['h', 't']),
                'reason': f'Mean reversion: {win_rate:.1%} win rate is too low'
            }
        
        return None
    
    def _calculate_bet_size(self, confidence: float) -> int:
        """Calculate bet size based on confidence and bankroll"""
        # Kelly Criterion inspired sizing
        # Higher confidence = larger bet (but capped)
        
        max_bet_percentage = 0.05  # Never bet more than 5% of bankroll
        min_bet = self.base_bet
        
        # Scale bet size with confidence
        bet_percentage = confidence * max_bet_percentage
        calculated_bet = int(self.current_bankroll * bet_percentage)
        
        # Ensure bet is within reasonable bounds
        bet_amount = max(min_bet, min(calculated_bet, self.max_bet))
        bet_amount = min(bet_amount, self.current_bankroll)
        
        return bet_amount
    
    def get_arbitrage_stats(self) -> Dict[str, Any]:
        """Get arbitrage-specific statistics"""
        base_stats = self.get_stats()
        
        if not self.recent_results:
            return {**base_stats, 'arbitrage_data': 'No data yet'}
        
        recent_win_rate = sum(1 for r in self.recent_results if r['won']) / len(self.recent_results)
        
        # Current streak analysis
        current_streak = 0
        streak_type = None
        for result in reversed(self.recent_results):
            if streak_type is None:
                streak_type = 'win' if result['won'] else 'loss'
                current_streak = 1
            elif (streak_type == 'win' and result['won']) or (streak_type == 'loss' and not result['won']):
                current_streak += 1
            else:
                break
        
        arbitrage_data = {
            'recent_results_count': len(self.recent_results),
            'recent_win_rate': recent_win_rate,
            'current_streak': f"{current_streak} {streak_type}s" if streak_type else "None",
            'next_opportunity': self._analyze_arbitrage_opportunity()
        }
        
        return {**base_stats, 'arbitrage_data': arbitrage_data}
