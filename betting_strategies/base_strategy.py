from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

class BetResult(Enum):
    WIN = "win"
    LOSS = "loss"
    PENDING = "pending"
    ERROR = "error"

@dataclass
class BetInfo:
    amount: int
    side: Optional[str]  # 'h' for heads, 't' for tails, None for random
    timestamp: float
    result: BetResult = BetResult.PENDING
    profit: int = 0
    message_id: Optional[str] = None

class BaseBettingStrategy(ABC):
    """Abstract base class for all betting strategies"""
    
    def __init__(self, initial_bankroll: int, max_bet: int = 1000):
        self.initial_bankroll = initial_bankroll
        self.current_bankroll = initial_bankroll
        self.max_bet = max_bet
        self.bet_history = []
        self.consecutive_losses = 0
        self.consecutive_wins = 0
        self.total_bets = 0
        self.total_wins = 0
        self.total_losses = 0
        
    @abstractmethod
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """
        Calculate the next bet amount and side
        Returns None if no bet should be placed
        """
        pass
    
    @abstractmethod
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update strategy state after a bet result"""
        pass
    
    def should_stop_betting(self) -> bool:
        """Check if betting should stop due to risk management rules"""
        # Stop if bankroll is too low
        if self.current_bankroll <= 0:
            return True

        # Stop if bankroll below minimum threshold
        if self.current_bankroll < 1000:  # MIN_BANKROLL_THRESHOLD
            return True

        # Stop if too many consecutive losses (10 losses)
        if self.consecutive_losses >= 10:
            return True

        # Stop if lost too much of initial bankroll (30%)
        loss_percentage = (self.initial_bankroll - self.current_bankroll) / self.initial_bankroll
        if loss_percentage >= 0.3:
            return True

        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current strategy statistics"""
        if self.total_bets == 0:
            win_rate = 0
        else:
            win_rate = self.total_wins / self.total_bets
            
        profit = self.current_bankroll - self.initial_bankroll
        roi = profit / self.initial_bankroll if self.initial_bankroll > 0 else 0
        
        return {
            'initial_bankroll': self.initial_bankroll,
            'current_bankroll': self.current_bankroll,
            'profit': profit,
            'roi': roi,
            'total_bets': self.total_bets,
            'total_wins': self.total_wins,
            'total_losses': self.total_losses,
            'win_rate': win_rate,
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses
        }
    
    def _update_base_stats(self, won: bool, profit: int):
        """Update base statistics"""
        self.current_bankroll += profit
        self.total_bets += 1
        
        if won:
            self.total_wins += 1
            self.consecutive_wins += 1
            self.consecutive_losses = 0
        else:
            self.total_losses += 1
            self.consecutive_losses += 1
            self.consecutive_wins = 0
