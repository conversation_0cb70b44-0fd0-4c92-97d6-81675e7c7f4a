import time
from typing import Optional
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult

class MartingaleStrategy(BaseBettingStrategy):
    """
    Martingale strategy: Double bet after each loss, reset to base after win
    """
    
    def __init__(self, initial_bankroll: int, base_bet: int, multiplier: float = 2.0, side: Optional[str] = None):
        super().__init__(initial_bankroll)
        self.base_bet = base_bet
        self.multiplier = multiplier
        self.side = side
        self.current_bet_amount = base_bet
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate next bet using Martingale progression"""
        if self.should_stop_betting():
            return None
            
        # Check if we have enough bankroll for the current bet
        if self.current_bankroll < self.current_bet_amount:
            print(f"⚠️ Insufficient bankroll for Martingale bet: {self.current_bet_amount}")
            return None
            
        # Don't bet more than max bet limit
        bet_amount = min(self.current_bet_amount, self.max_bet)
        
        return BetInfo(
            amount=bet_amount,
            side=self.side,
            timestamp=time.time()
        )
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update Martingale progression based on result"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
        
        if won:
            # Reset to base bet after win
            self.current_bet_amount = self.base_bet
            print(f"✅ Win! Resetting bet to base amount: {self.base_bet}")
        else:
            # Double the bet after loss
            self.current_bet_amount = int(self.current_bet_amount * self.multiplier)
            print(f"❌ Loss! Next bet amount: {self.current_bet_amount}")
    
    def should_stop_betting(self) -> bool:
        """Enhanced stop conditions for Martingale"""
        if super().should_stop_betting():
            return True
            
        # Stop if next bet would be too large relative to bankroll
        if self.current_bet_amount > self.current_bankroll * 0.5:
            print("⚠️ Next bet too large relative to bankroll")
            return True
            
        return False
