import json
import pickle
import os
from typing import Dict, Any, List
from collections import deque, Counter

class PersistentStorage:
    """
    Handles saving and loading strategy learning data to disk
    """
    
    def __init__(self, strategy_name: str):
        self.strategy_name = strategy_name
        self.data_dir = "strategy_data"
        self.ensure_data_dir()
        
    def ensure_data_dir(self):
        """Create data directory if it doesn't exist"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def get_file_path(self, data_type: str) -> str:
        """Get file path for specific data type"""
        return os.path.join(self.data_dir, f"{self.strategy_name}_{data_type}.json")
    
    def save_data(self, data_type: str, data: Any) -> bool:
        """Save data to disk"""
        try:
            file_path = self.get_file_path(data_type)
            
            # Convert special objects to serializable format
            serializable_data = self._make_serializable(data)
            
            with open(file_path, 'w') as f:
                json.dump(serializable_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"❌ Error saving {data_type}: {e}")
            return False
    
    def load_data(self, data_type: str, default_value: Any = None) -> Any:
        """Load data from disk"""
        try:
            file_path = self.get_file_path(data_type)
            
            if not os.path.exists(file_path):
                return default_value
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Convert back to original format
            return self._restore_from_serializable(data, default_value)
            
        except Exception as e:
            print(f"❌ Error loading {data_type}: {e}")
            return default_value
    
    def _make_serializable(self, data: Any) -> Any:
        """Convert data to JSON-serializable format"""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                # Convert tuple keys to strings
                str_key = str(key) if isinstance(key, tuple) else key
                result[str_key] = self._make_serializable(value)
            return result
        
        elif isinstance(data, (deque, list)):
            return list(data)
        
        elif isinstance(data, Counter):
            return dict(data)
        
        elif isinstance(data, tuple):
            return list(data)
        
        else:
            return data
    
    def _restore_from_serializable(self, data: Any, default_value: Any) -> Any:
        """Restore data from JSON format"""
        if default_value is None:
            return data
        
        # Restore based on the type of default_value
        if isinstance(default_value, deque):
            maxlen = default_value.maxlen
            return deque(data if data else [], maxlen=maxlen)
        
        elif isinstance(default_value, Counter):
            return Counter(data if data else {})
        
        elif isinstance(default_value, dict):
            if not data:
                return {}
            
            result = {}
            for key, value in data.items():
                # Try to restore tuple keys
                try:
                    # Check if key looks like a tuple string
                    if key.startswith('(') and key.endswith(')'):
                        # Parse tuple string back to tuple
                        tuple_key = eval(key)
                        if isinstance(tuple_key, tuple):
                            key = tuple_key
                except:
                    pass  # Keep as string if parsing fails
                
                # Restore nested structures
                if isinstance(value, dict):
                    result[key] = Counter(value)
                else:
                    result[key] = value
            
            return result
        
        else:
            return data if data is not None else default_value

class PersistentAdvancedMathematical:
    """
    Mixin class to add persistence to AdvancedMathematicalStrategy
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.storage = PersistentStorage("advanced_mathematical")
        self.load_learning_data()
    
    def load_learning_data(self):
        """Load all learning data from disk"""
        print("📚 Loading previous learning data...")
        
        # Load result history
        self.result_history = self.storage.load_data("result_history", deque(maxlen=100))
        
        # Load pattern memory
        self.pattern_memory = self.storage.load_data("pattern_memory", {})
        
        # Load Markov chain data
        self.transition_matrix = self.storage.load_data("transition_matrix", {})
        self.state_frequencies = self.storage.load_data("state_frequencies", Counter())
        
        # Load other learning parameters
        learning_params = self.storage.load_data("learning_params", {})
        if learning_params:
            self.win_probability = learning_params.get("win_probability", 0.5)
            self.confidence_multiplier = learning_params.get("confidence_multiplier", 1.0)
            self.consecutive_bet_count = learning_params.get("consecutive_bet_count", 0)
        
        print(f"✅ Loaded: {len(self.result_history)} results, {len(self.pattern_memory)} patterns")
        print(f"✅ Loaded: {len(self.transition_matrix)} Markov states")
    
    def save_learning_data(self):
        """Save all learning data to disk"""
        print("💾 Saving learning data...")
        
        # Save result history
        self.storage.save_data("result_history", self.result_history)
        
        # Save pattern memory
        self.storage.save_data("pattern_memory", self.pattern_memory)
        
        # Save Markov chain data
        self.storage.save_data("transition_matrix", self.transition_matrix)
        self.storage.save_data("state_frequencies", self.state_frequencies)
        
        # Save learning parameters
        learning_params = {
            "win_probability": self.win_probability,
            "confidence_multiplier": self.confidence_multiplier,
            "consecutive_bet_count": self.consecutive_bet_count
        }
        self.storage.save_data("learning_params", learning_params)
        
        print("✅ Learning data saved successfully!")
    
    def update_result(self, bet_info, won: bool, profit: int):
        """Override to save data after each update"""
        super().update_result(bet_info, won, profit)
        
        # Save learning data after every 5 bets
        if len(self.bet_history) % 5 == 0:
            self.save_learning_data()

class PersistentOwoPatternAnalyzer:
    """
    Mixin class to add persistence to OwoPatternAnalyzer
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.storage = PersistentStorage("owo_pattern_analyzer")
        self.load_learning_data()
    
    def load_learning_data(self):
        """Load all learning data from disk"""
        print("📚 Loading owo pattern analysis data...")
        
        # Load owo results
        self.owo_results = self.storage.load_data("owo_results", deque(maxlen=200))
        
        # Load time patterns
        self.time_patterns = self.storage.load_data("time_patterns", {})
        
        # Load runs test data
        self.runs_test_data = self.storage.load_data("runs_test_data", deque(maxlen=50))
        
        # Load learning parameters
        learning_params = self.storage.load_data("learning_params", {})
        if learning_params:
            self.confidence_multiplier = learning_params.get("confidence_multiplier", 1.0)
            self.pattern_strength = learning_params.get("pattern_strength", 0.0)
        
        print(f"✅ Loaded: {len(self.owo_results)} owo results, {len(self.time_patterns)} time patterns")
    
    def save_learning_data(self):
        """Save all learning data to disk"""
        print("💾 Saving owo analysis data...")
        
        # Save owo results
        self.storage.save_data("owo_results", self.owo_results)
        
        # Save time patterns
        self.storage.save_data("time_patterns", self.time_patterns)
        
        # Save runs test data
        self.storage.save_data("runs_test_data", self.runs_test_data)
        
        # Save learning parameters
        learning_params = {
            "confidence_multiplier": self.confidence_multiplier,
            "pattern_strength": self.pattern_strength
        }
        self.storage.save_data("learning_params", learning_params)
        
        print("✅ Owo analysis data saved successfully!")
    
    def update_result(self, bet_info, won: bool, profit: int):
        """Override to save data after each update"""
        super().update_result(bet_info, won, profit)
        
        # Save learning data after every 3 bets
        if len(self.bet_history) % 3 == 0:
            self.save_learning_data()
