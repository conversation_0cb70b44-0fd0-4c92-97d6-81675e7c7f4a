import time
import random
from typing import Optional, List
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult

class ProgressiveStrategy(BaseBettingStrategy):
    """
    Progressive betting strategy with multiple progression types:
    1. Fibonacci progression
    2. D'<PERSON>embert progression  
    3. Custom adaptive progression
    """
    
    def __init__(self, initial_bankroll: int, base_bet: int, progression_type: str = "fibonacci"):
        super().__init__(initial_bankroll)
        self.base_bet = base_bet
        self.progression_type = progression_type
        self.current_step = 0
        
        # Fibonacci sequence for progression
        self.fibonacci_sequence = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144]
        
        # D'Alembert progression
        self.dalembert_step = 1
        
        # Custom progression (more conservative)
        self.custom_sequence = [1, 1, 2, 2, 3, 4, 5, 7, 10, 15, 20]
        
        # Bet amount variations
        self.bet_variations = [0.8, 0.9, 1.0, 1.1, 1.2, 1.3]
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate next bet using selected progression"""
        if self.should_stop_betting():
            return None
        
        # Calculate base amount using progression
        if self.progression_type == "fibonacci":
            multiplier = self.fibonacci_sequence[min(self.current_step, len(self.fibonacci_sequence) - 1)]
        elif self.progression_type == "dalembert":
            multiplier = max(1, 1 + self.dalembert_step)
        elif self.progression_type == "custom":
            multiplier = self.custom_sequence[min(self.current_step, len(self.custom_sequence) - 1)]
        else:
            multiplier = 1
        
        # Add variation to make bets less predictable
        variation = random.choice(self.bet_variations)
        
        # Calculate bet amount
        bet_amount = int(self.base_bet * multiplier * variation)
        
        # Ensure bet is within reasonable bounds
        max_bet = min(self.current_bankroll * 0.2, 2000)  # Max 20% of bankroll or 2000
        bet_amount = min(bet_amount, max_bet)
        bet_amount = max(bet_amount, 10)  # Minimum bet
        
        # Choose side with some strategy
        side = self._choose_side()
        
        print(f"🔢 {self.progression_type.title()} progression:")
        print(f"   Step: {self.current_step} | Multiplier: {multiplier} | Variation: {variation:.1f}")
        print(f"   Base: {self.base_bet} | Final: {bet_amount}")
        
        return BetInfo(
            amount=bet_amount,
            side=side,
            timestamp=time.time()
        )
    
    def _choose_side(self) -> Optional[str]:
        """Choose betting side with some logic"""
        if len(self.bet_history) < 3:
            return random.choice(['h', 't'])
        
        # Look at recent results
        recent_losses = 0
        for bet in reversed(self.bet_history[-5:]):
            if bet.result == BetResult.LOSS:
                recent_losses += 1
            else:
                break
        
        # If on a losing streak, try to break it with opposite of last choice
        if recent_losses >= 3:
            last_bet = self.bet_history[-1]
            if last_bet.side == 'h':
                return 't'
            elif last_bet.side == 't':
                return 'h'
        
        # Otherwise random with slight bias
        return random.choice(['h', 't', 'h', 't', random.choice(['h', 't'])])
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update progression based on result"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
        
        if won:
            # Reset progression on win
            self.current_step = 0
            self.dalembert_step = max(0, self.dalembert_step - 1)
            print(f"✅ Win! Reset progression to step {self.current_step}")
        else:
            # Advance progression on loss
            if self.progression_type == "fibonacci":
                self.current_step = min(self.current_step + 1, len(self.fibonacci_sequence) - 1)
            elif self.progression_type == "dalembert":
                self.dalembert_step += 1
            elif self.progression_type == "custom":
                self.current_step = min(self.current_step + 1, len(self.custom_sequence) - 1)
            
            print(f"❌ Loss! Advanced progression to step {self.current_step}")
    
    def should_stop_betting(self) -> bool:
        """Enhanced stop conditions for progressive strategy"""
        if super().should_stop_betting():
            return True
        
        # Stop if progression has gone too far
        if self.progression_type == "fibonacci" and self.current_step >= len(self.fibonacci_sequence) - 2:
            print("🛑 Stopping: Fibonacci progression at maximum")
            return True
        
        if self.progression_type == "custom" and self.current_step >= len(self.custom_sequence) - 2:
            print("🛑 Stopping: Custom progression at maximum")
            return True
        
        if self.dalembert_step > 20:
            print("🛑 Stopping: D'Alembert progression too high")
            return True
        
        return False
