import time
import random
import math
from typing import Optional, Dict, Any, List, Tuple
from collections import deque, Counter
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult

class AdvancedMathematicalStrategy(BaseBettingStrategy):
    """
    Advanced mathematical strategy using:
    1. Markov Chain analysis for pattern prediction
    2. <PERSON> for optimal bet sizing
    3. Streak analysis and mean reversion
    4. <PERSON> simulation for risk assessment
    5. Pattern recognition algorithms
    6. Anti-martingale (reverse martingale) for winning streaks
    """
    
    def __init__(self, initial_bankroll: int, max_bet_percentage: float = 0.02):
        super().__init__(initial_bankroll)
        self.max_bet_percentage = max_bet_percentage  # Max 2% of bankroll per bet
        
        # Pattern analysis
        self.result_history = deque(maxlen=100)  # Last 100 results (H/T)
        self.pattern_memory = {}  # Pattern -> next result frequency
        self.min_pattern_length = 2
        self.max_pattern_length = 8
        
        # Markov chain states
        self.transition_matrix = {}
        self.state_frequencies = Counter()
        
        # Streak analysis
        self.current_streak = {'type': None, 'length': 0}
        self.streak_history = []
        
        # Kelly Criterion parameters
        self.win_probability = 0.5  # Start with 50% assumption
        self.edge_threshold = 0.02  # Minimum 2% edge to bet
        
        # Risk management
        self.max_consecutive_bets = 5  # Max bets without a break
        self.consecutive_bet_count = 0
        self.forced_break_probability = 0.1  # 10% chance of random break
        
        # Pattern confidence tracking
        self.pattern_success_rate = {}
        self.confidence_threshold = 0.6  # 60% confidence minimum
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate optimal bet using advanced mathematical analysis"""
        if self.should_stop_betting():
            return None
        
        # Force occasional breaks to avoid predictable patterns
        if (self.consecutive_bet_count >= self.max_consecutive_bets or 
            random.random() < self.forced_break_probability):
            print("🔄 Taking strategic break to avoid predictable patterns")
            self.consecutive_bet_count = 0
            return None
        
        # Analyze patterns and predict next outcome
        prediction = self._predict_next_outcome()
        
        if not prediction:
            print("⚠️ No prediction available, skipping bet")
            return None

        if prediction['confidence'] < self.confidence_threshold:
            print(f"⚠️ Low confidence prediction ({prediction['confidence']:.1%}), skipping bet")
            return None
        
        # Calculate optimal bet size using Kelly Criterion
        bet_amount = self._calculate_kelly_bet_size(prediction)
        
        if bet_amount < 10:  # Minimum bet threshold
            print("💰 Calculated bet too small, skipping")
            return None
        
        side = prediction['predicted_side']
        self.consecutive_bet_count += 1
        
        print(f"🧠 Mathematical Analysis:")
        print(f"   Prediction: {side} ({prediction['confidence']:.1%} confidence)")
        print(f"   Method: {prediction['method']}")
        print(f"   Kelly bet: {bet_amount}")
        
        return BetInfo(
            amount=bet_amount,
            side=side,
            timestamp=time.time()
        )
    
    def _predict_next_outcome(self) -> Optional[Dict[str, Any]]:
        """Predict next outcome using multiple mathematical methods"""
        if len(self.result_history) < 5:
            return None
        
        predictions = []
        
        # Method 1: Pattern matching
        pattern_pred = self._pattern_matching_prediction()
        if pattern_pred:
            predictions.append(pattern_pred)
        
        # Method 2: Markov chain analysis
        markov_pred = self._markov_chain_prediction()
        if markov_pred:
            predictions.append(markov_pred)
        
        # Method 3: Streak analysis and mean reversion
        streak_pred = self._streak_analysis_prediction()
        if streak_pred:
            predictions.append(streak_pred)
        
        # Method 4: Frequency analysis
        freq_pred = self._frequency_analysis_prediction()
        if freq_pred:
            predictions.append(freq_pred)
        
        # Combine predictions using weighted average
        return self._combine_predictions(predictions)
    
    def _pattern_matching_prediction(self) -> Optional[Dict[str, Any]]:
        """Find patterns in recent results and predict next outcome"""
        recent_results = list(self.result_history)[-20:]  # Last 20 results
        
        best_pattern = None
        best_confidence = 0
        
        # Try different pattern lengths
        for length in range(self.min_pattern_length, min(self.max_pattern_length, len(recent_results))):
            pattern = tuple(recent_results[-length:])
            
            if pattern in self.pattern_memory:
                next_outcomes = self.pattern_memory[pattern]
                total = sum(next_outcomes.values())
                
                if total >= 3:  # Need at least 3 occurrences
                    h_prob = next_outcomes.get('h', 0) / total
                    t_prob = next_outcomes.get('t', 0) / total
                    
                    confidence = max(h_prob, t_prob)
                    predicted_side = 'h' if h_prob > t_prob else 't'
                    
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_pattern = {
                            'predicted_side': predicted_side,
                            'confidence': confidence,
                            'method': f'Pattern-{length}: {pattern}',
                            'weight': 0.3
                        }
        
        return best_pattern
    
    def _markov_chain_prediction(self) -> Optional[Dict[str, Any]]:
        """Use Markov chain analysis for prediction"""
        if len(self.result_history) < 3:
            return None
        
        # Use last 2 results as current state
        current_state = tuple(list(self.result_history)[-2:])
        
        if current_state in self.transition_matrix:
            transitions = self.transition_matrix[current_state]
            total = sum(transitions.values())
            
            if total >= 2:  # Need at least 2 transitions
                h_prob = transitions.get('h', 0) / total
                t_prob = transitions.get('t', 0) / total
                
                confidence = max(h_prob, t_prob)
                predicted_side = 'h' if h_prob > t_prob else 't'
                
                return {
                    'predicted_side': predicted_side,
                    'confidence': confidence,
                    'method': f'Markov: {current_state}',
                    'weight': 0.25
                }
        
        return None
    
    def _streak_analysis_prediction(self) -> Optional[Dict[str, Any]]:
        """Analyze streaks and predict mean reversion"""
        if len(self.result_history) < 3:
            return None
        
        recent = list(self.result_history)[-10:]
        
        # Count current streak
        current_streak_length = 1
        current_streak_type = recent[-1]
        
        for i in range(len(recent) - 2, -1, -1):
            if recent[i] == current_streak_type:
                current_streak_length += 1
            else:
                break
        
        # Mean reversion probability increases with streak length
        if current_streak_length >= 3:
            # Probability of streak breaking increases exponentially
            break_probability = min(0.8, 0.4 + (current_streak_length - 3) * 0.1)
            opposite_side = 't' if current_streak_type == 'h' else 'h'
            
            return {
                'predicted_side': opposite_side,
                'confidence': break_probability,
                'method': f'Streak-break: {current_streak_length} {current_streak_type}s',
                'weight': 0.2
            }
        
        return None
    
    def _frequency_analysis_prediction(self) -> Optional[Dict[str, Any]]:
        """Analyze frequency imbalance and predict correction"""
        if len(self.result_history) < 10:
            return None
        
        recent = list(self.result_history)[-20:]  # Last 20 results
        h_count = recent.count('h')
        t_count = recent.count('t')
        total = len(recent)
        
        h_freq = h_count / total
        t_freq = t_count / total
        
        # If significant imbalance, predict correction
        imbalance_threshold = 0.15  # 15% deviation from 50%
        
        if abs(h_freq - 0.5) > imbalance_threshold:
            # Predict the underrepresented side
            if h_freq < 0.5:
                predicted_side = 'h'
                confidence = 0.5 + (0.5 - h_freq)
            else:
                predicted_side = 't'
                confidence = 0.5 + (0.5 - t_freq)
            
            return {
                'predicted_side': predicted_side,
                'confidence': min(confidence, 0.7),
                'method': f'Frequency: H={h_freq:.1%}, T={t_freq:.1%}',
                'weight': 0.25
            }
        
        return None
    
    def _combine_predictions(self, predictions: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Combine multiple predictions using weighted confidence"""
        if not predictions:
            return None
        
        # Weight predictions by their confidence and method weight
        h_score = 0
        t_score = 0
        total_weight = 0
        methods = []
        
        for pred in predictions:
            weight = pred['weight'] * pred['confidence']
            total_weight += weight
            methods.append(pred['method'])
            
            if pred['predicted_side'] == 'h':
                h_score += weight
            else:
                t_score += weight
        
        if total_weight == 0:
            return None
        
        # Normalize scores
        h_prob = h_score / total_weight
        t_prob = t_score / total_weight
        
        predicted_side = 'h' if h_prob > t_prob else 't'
        confidence = max(h_prob, t_prob)
        
        return {
            'predicted_side': predicted_side,
            'confidence': confidence,
            'method': f"Combined: {', '.join(methods)}",
            'weight': 1.0
        }
    
    def _calculate_kelly_bet_size(self, prediction: Dict[str, Any]) -> int:
        """Calculate optimal bet size using Kelly Criterion"""
        # Kelly formula: f = (bp - q) / b
        # where b = odds-1, p = win probability, q = lose probability
        
        confidence = prediction['confidence']
        
        # Assume even odds (1:1), so b = 1
        b = 1
        p = confidence  # Win probability based on our prediction
        q = 1 - p       # Lose probability
        
        # Kelly fraction
        kelly_fraction = (b * p - q) / b
        
        # Be conservative - use fractional Kelly
        conservative_kelly = kelly_fraction * 0.25  # Use 25% of Kelly
        
        # Apply maximum bet percentage limit
        max_fraction = min(conservative_kelly, self.max_bet_percentage)
        
        # Ensure positive bet size
        if max_fraction <= 0:
            return 0
        
        bet_amount = int(self.current_bankroll * max_fraction)
        
        # Apply minimum and maximum limits
        bet_amount = max(10, min(bet_amount, 500))  # Between 10 and 500
        bet_amount = min(bet_amount, self.current_bankroll)
        
        return bet_amount
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update all mathematical models with new result"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
        
        # Extract actual result from bet info (we need to get this from the response parser)
        # For now, we'll infer it from the bet outcome
        if hasattr(bet_info, 'actual_result'):
            actual_result = bet_info.actual_result
        else:
            # Infer from win/loss and chosen side
            if won:
                actual_result = bet_info.side  # If won, actual result matches chosen side
            else:
                actual_result = 't' if bet_info.side == 'h' else 'h'  # If lost, opposite
        
        # Update result history
        self.result_history.append(actual_result)
        
        # Update pattern memory
        self._update_pattern_memory(actual_result)
        
        # Update Markov chain
        self._update_markov_chain(actual_result)
        
        # Update win probability estimate
        self._update_win_probability()
        
        # Reset consecutive bet counter on break
        if random.random() < 0.1:  # 10% chance to reset
            self.consecutive_bet_count = 0
        
        print(f"📊 Model updated: {len(self.result_history)} results, {len(self.pattern_memory)} patterns")
    
    def _update_pattern_memory(self, actual_result: str):
        """Update pattern memory with new result"""
        recent_results = list(self.result_history)[:-1]  # Exclude the just-added result
        
        for length in range(self.min_pattern_length, min(self.max_pattern_length, len(recent_results) + 1)):
            if len(recent_results) >= length:
                pattern = tuple(recent_results[-length:])
                
                if pattern not in self.pattern_memory:
                    self.pattern_memory[pattern] = Counter()
                
                self.pattern_memory[pattern][actual_result] += 1
    
    def _update_markov_chain(self, actual_result: str):
        """Update Markov chain transition matrix"""
        if len(self.result_history) >= 3:
            # Use previous 2 results as state
            state = tuple(list(self.result_history)[-3:-1])
            
            if state not in self.transition_matrix:
                self.transition_matrix[state] = Counter()
            
            self.transition_matrix[state][actual_result] += 1
            self.state_frequencies[state] += 1
    
    def _update_win_probability(self):
        """Update overall win probability estimate"""
        if self.total_bets > 0:
            self.win_probability = self.total_wins / self.total_bets
    
    def should_stop_betting(self) -> bool:
        """Enhanced stop conditions with mathematical analysis"""
        if super().should_stop_betting():
            return True
        
        # Stop if mathematical models show poor performance
        if self.total_bets >= 20:
            recent_performance = self.total_wins / self.total_bets
            if recent_performance < 0.35:  # Less than 35% win rate
                print(f"🛑 Stopping: Poor mathematical performance ({recent_performance:.1%})")
                return True
        
        return False
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed mathematical strategy information"""
        base_stats = self.get_stats()
        
        return {
            **base_stats,
            'strategy_type': 'Advanced Mathematical',
            'patterns_learned': len(self.pattern_memory),
            'markov_states': len(self.transition_matrix),
            'result_history_size': len(self.result_history),
            'estimated_win_probability': self.win_probability,
            'consecutive_bets': self.consecutive_bet_count
        }
