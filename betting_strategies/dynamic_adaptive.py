import time
import random
import math
from typing import Optional, Dict, Any
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult

class DynamicAdaptiveStrategy(BaseBettingStrategy):
    """
    Advanced dynamic strategy that adapts bet amounts based on:
    1. Current bankroll percentage
    2. Recent performance
    3. Streak analysis
    4. Risk-adjusted sizing
    """
    
    def __init__(self, initial_bankroll: int, base_bet_percentage: float = 0.02):
        super().__init__(initial_bankroll)
        self.base_bet_percentage = base_bet_percentage  # 2% of bankroll as base
        self.min_bet = 10
        self.max_bet = min(1000, initial_bankroll * 0.1)  # Max 10% of initial bankroll
        
        # Dynamic parameters
        self.confidence_multiplier = 1.0
        self.recent_performance_window = 10
        self.streak_bonus_threshold = 3
        
        # Bet amount variation
        self.bet_variation_range = 0.3  # ±30% variation
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate dynamic bet amount based on multiple factors"""
        if self.should_stop_betting():
            return None
        
        # Base bet amount (percentage of current bankroll)
        base_amount = self.current_bankroll * self.base_bet_percentage
        
        # Apply performance adjustments
        performance_multiplier = self._calculate_performance_multiplier()
        
        # Apply streak adjustments
        streak_multiplier = self._calculate_streak_multiplier()
        
        # Apply confidence adjustments
        confidence_multiplier = self._calculate_confidence_multiplier()
        
        # Add random variation to make bets less predictable
        variation = random.uniform(1 - self.bet_variation_range, 1 + self.bet_variation_range)
        
        # Calculate final bet amount
        final_amount = base_amount * performance_multiplier * streak_multiplier * confidence_multiplier * variation
        
        # Ensure bet is within bounds
        final_amount = max(self.min_bet, min(final_amount, self.max_bet))
        final_amount = min(final_amount, self.current_bankroll * 0.15)  # Never bet more than 15% of current bankroll
        final_amount = int(final_amount)
        
        # Choose side based on recent patterns
        side = self._choose_optimal_side()
        
        print(f"🎯 Dynamic bet calculation:")
        print(f"   Base: {base_amount:.1f} | Perf: {performance_multiplier:.2f} | Streak: {streak_multiplier:.2f}")
        print(f"   Conf: {confidence_multiplier:.2f} | Var: {variation:.2f} | Final: {final_amount}")
        
        return BetInfo(
            amount=final_amount,
            side=side,
            timestamp=time.time()
        )
    
    def _calculate_performance_multiplier(self) -> float:
        """Adjust bet size based on recent performance"""
        if len(self.bet_history) < 3:
            return 1.0
        
        # Look at recent bets
        recent_bets = self.bet_history[-self.recent_performance_window:]
        if not recent_bets:
            return 1.0
        
        # Calculate recent win rate
        recent_wins = sum(1 for bet in recent_bets if bet.result == BetResult.WIN)
        win_rate = recent_wins / len(recent_bets)
        
        # Calculate recent profit rate
        recent_profit = sum(bet.profit for bet in recent_bets)
        recent_wagered = sum(bet.amount for bet in recent_bets)
        profit_rate = recent_profit / recent_wagered if recent_wagered > 0 else 0
        
        # Increase bets when performing well, decrease when performing poorly
        if win_rate > 0.6:  # Winning more than 60%
            return 1.3
        elif win_rate > 0.5:  # Winning more than 50%
            return 1.1
        elif win_rate < 0.3:  # Winning less than 30%
            return 0.6
        elif win_rate < 0.4:  # Winning less than 40%
            return 0.8
        else:
            return 1.0
    
    def _calculate_streak_multiplier(self) -> float:
        """Adjust bet size based on current streak"""
        if self.consecutive_wins >= self.streak_bonus_threshold:
            # On a winning streak - increase bets moderately
            return 1.0 + (self.consecutive_wins * 0.1)
        elif self.consecutive_losses >= 5:
            # On a losing streak - decrease bets significantly
            return max(0.4, 1.0 - (self.consecutive_losses * 0.1))
        else:
            return 1.0
    
    def _calculate_confidence_multiplier(self) -> float:
        """Adjust bet size based on overall confidence"""
        if self.total_bets < 5:
            return 0.7  # Conservative when starting
        
        overall_win_rate = self.total_wins / self.total_bets
        overall_roi = (self.current_bankroll - self.initial_bankroll) / self.initial_bankroll
        
        # High confidence if good win rate and positive ROI
        if overall_win_rate > 0.55 and overall_roi > 0.1:
            return 1.4
        elif overall_win_rate > 0.5 and overall_roi > 0:
            return 1.2
        elif overall_win_rate < 0.4 or overall_roi < -0.2:
            return 0.6
        else:
            return 1.0
    
    def _choose_optimal_side(self) -> Optional[str]:
        """Choose side based on recent patterns and analysis"""
        if len(self.bet_history) < 5:
            return random.choice(['h', 't'])
        
        # Analyze recent results for patterns
        recent_results = self.bet_history[-10:]
        
        # Count recent heads/tails results
        heads_count = 0
        tails_count = 0
        
        for bet in recent_results:
            if hasattr(bet, 'result_side'):
                if bet.result_side == 'heads':
                    heads_count += 1
                elif bet.result_side == 'tails':
                    tails_count += 1
        
        # If one side has been dominant, bet on the other (mean reversion)
        if heads_count > tails_count + 3:
            return 't'  # Bet tails
        elif tails_count > heads_count + 3:
            return 'h'  # Bet heads
        
        # Check for streaks in our own choices
        if len(recent_results) >= 3:
            last_3_sides = [bet.side for bet in recent_results[-3:] if bet.side]
            if len(set(last_3_sides)) == 1 and len(last_3_sides) == 3:
                # We've been betting the same side 3 times, switch
                return 't' if last_3_sides[0] == 'h' else 'h'
        
        # Default to random with slight bias based on recent performance
        if self.consecutive_wins > 2:
            # Keep doing what's working
            last_winning_bet = next((bet for bet in reversed(self.bet_history) 
                                   if bet.result == BetResult.WIN), None)
            if last_winning_bet and last_winning_bet.side:
                return last_winning_bet.side
        
        return random.choice(['h', 't'])
    
    def should_stop_betting(self) -> bool:
        """Enhanced stop conditions for dynamic strategy"""
        # Use base class conditions
        if super().should_stop_betting():
            return True
        
        # Stop if bankroll is too low
        if self.current_bankroll < 1000:
            print("🛑 Stopping: Bankroll below minimum threshold (1000)")
            return True
        
        # Stop if lost too much of initial bankroll
        loss_percentage = (self.initial_bankroll - self.current_bankroll) / self.initial_bankroll
        if loss_percentage >= 0.4:  # Stop if lost 40%
            print(f"🛑 Stopping: Lost {loss_percentage:.1%} of initial bankroll")
            return True
        
        # Stop after 10 consecutive losses
        if self.consecutive_losses >= 10:
            print("🛑 Stopping: 10 consecutive losses reached")
            return True
        
        return False
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update strategy with enhanced tracking"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
        
        # Update confidence based on result
        if won:
            self.confidence_multiplier = min(1.5, self.confidence_multiplier + 0.05)
        else:
            self.confidence_multiplier = max(0.5, self.confidence_multiplier - 0.03)
        
        # Print performance update
        if self.total_bets % 5 == 0:  # Every 5 bets
            win_rate = self.total_wins / self.total_bets
            roi = (self.current_bankroll - self.initial_bankroll) / self.initial_bankroll
            print(f"📊 Performance update: {win_rate:.1%} win rate, {roi:+.1%} ROI")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed strategy information"""
        base_stats = self.get_stats()
        
        return {
            **base_stats,
            'strategy_type': 'Dynamic Adaptive',
            'confidence_multiplier': self.confidence_multiplier,
            'current_bet_range': f"{self.min_bet}-{min(self.max_bet, int(self.current_bankroll * 0.15))}",
            'base_bet_percentage': f"{self.base_bet_percentage:.1%}",
            'next_estimated_bet': int(self.current_bankroll * self.base_bet_percentage) if self.current_bankroll > 0 else 0
        }
