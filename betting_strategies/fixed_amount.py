import time
from typing import Optional
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult

class FixedAmountStrategy(BaseBettingStrategy):
    """Simple strategy that bets a fixed amount every time"""
    
    def __init__(self, initial_bankroll: int, bet_amount: int, side: Optional[str] = None):
        super().__init__(initial_bankroll)
        self.bet_amount = bet_amount
        self.side = side  # 'h', 't', or None for random
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate next bet - always the same amount"""
        if self.should_stop_betting():
            return None
            
        if self.current_bankroll < self.bet_amount:
            return None
            
        return BetInfo(
            amount=self.bet_amount,
            side=self.side,
            timestamp=time.time()
        )
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update strategy after bet result"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
