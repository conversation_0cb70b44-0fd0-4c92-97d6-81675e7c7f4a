#!/usr/bin/env python3
"""
Analyze owo bot responses to understand the win/loss logic
"""

from discord_client import DiscordClient
from owo_response_parser import OwoResponseParser
import json

def main():
    client = DiscordClient()
    parser = OwoResponseParser()
    
    print("🔍 Analyzing owo bot responses...")
    messages = client.get_recent_messages(limit=20)
    
    if not messages:
        print("❌ Failed to fetch messages")
        return
    
    print(f"\n📥 Found {len(messages)} messages")
    print("=" * 100)
    
    owo_responses = []
    
    for msg in messages:
        author = msg.get('author', {})
        author_name = author.get('username', 'Unknown')
        content = msg.get('content', '')
        
        if author_name.lower() == 'owo' and parser.is_coinflip_response(content):
            owo_responses.append(content)
    
    print(f"\n🎰 Found {len(owo_responses)} owo coinflip responses:")
    print("=" * 100)
    
    for i, response in enumerate(owo_responses):
        print(f"\n📝 RESPONSE #{i+1}:")
        print(f"Content: {response}")
        print("-" * 50)
        
        # Analyze with our parser
        analysis = parser.debug_parse(response)
        print("🔍 PARSER ANALYSIS:")
        for key, value in analysis.items():
            if key == 'original_content':
                continue
            print(f"  {key}: {value}")
        
        if analysis.get('inconsistency'):
            print("⚠️  INCONSISTENCY DETECTED!")
            print(f"  Logical result: {analysis['logical_win']} (based on sides)")
            print(f"  Detected result: {analysis['win_loss_detected']} (based on text)")
        
        print("=" * 100)

if __name__ == "__main__":
    main()
