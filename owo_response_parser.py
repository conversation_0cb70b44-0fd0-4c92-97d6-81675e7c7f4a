import re
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass

@dataclass
class BetResult:
    won: bool
    amount_bet: int
    amount_won: int
    profit: int  # amount_won - amount_bet
    side_chosen: Optional[str]  # 'heads' or 'tails'
    side_result: Optional[str]  # actual result
    new_balance: Optional[int] = None

class OwoResponseParser:
    """Parse owo bot responses to extract betting results"""
    
    def __init__(self):
        # Common patterns in owo bot responses
        self.coinflip_patterns = [
            # Pattern for coinflip results
            r"(?i).*coinflip.*",
            r"(?i).*\b(heads|tails)\b.*",
            r"(?i).*\b(won|lost)\b.*",
            r"(?i).*\bcf\b.*",
        ]
        
        # Patterns to extract amounts
        self.amount_patterns = [
            r"(?i).*won.*?(\d{1,3}(?:,\d{3})*|\d+).*",
            r"(?i).*lost.*?(\d{1,3}(?:,\d{3})*|\d+).*",
            r"(?i).*bet.*?(\d{1,3}(?:,\d{3})*|\d+).*",
        ]
        
        # Patterns for balance
        self.balance_patterns = [
            r"(?i).*balance.*?(\d{1,3}(?:,\d{3})*|\d+).*",
            r"(?i).*wallet.*?(\d{1,3}(?:,\d{3})*|\d+).*",
        ]
    
    def is_coinflip_response(self, message_content: str) -> bool:
        """Check if message is a coinflip response from owo bot"""
        content = message_content.lower()
        
        # Check for coinflip keywords
        coinflip_keywords = ['coinflip', 'cf', 'heads', 'tails', 'won', 'lost']
        return any(keyword in content for keyword in coinflip_keywords)
    
    def parse_coinflip_result(self, message_content: str, bet_amount: int) -> Optional[BetResult]:
        """
        Parse owo bot coinflip response to extract result
        
        Args:
            message_content: The owo bot's response message
            bet_amount: The amount that was bet
            
        Returns:
            BetResult object with parsed information, or None if parsing failed
        """
        content = message_content.lower()
        
        if not self.is_coinflip_response(content):
            return None
        
        # Determine if won or lost
        won = self._determine_win_loss(content)
        if won is None:
            return None
        
        # Extract amounts
        amounts = self._extract_amounts(content)
        
        # Extract sides (what was chosen vs what came up)
        side_chosen, side_result = self._extract_sides(content)
        
        # Calculate profit
        if won:
            # Usually owo doubles your bet when you win
            amount_won = bet_amount * 2
            profit = amount_won - bet_amount
        else:
            amount_won = 0
            profit = -bet_amount
        
        # Try to extract balance
        new_balance = self._extract_balance(content)
        
        result = BetResult(
            won=won,
            amount_bet=bet_amount,
            amount_won=amount_won,
            profit=profit,
            side_chosen=side_chosen,
            side_result=side_result,
            new_balance=new_balance
        )

        # Store the actual result for advanced strategies
        result.actual_result = side_result

        return result
    
    def _determine_win_loss(self, content: str) -> Optional[bool]:
        """Determine if the bet was won or lost"""
        # Look for explicit win indicators first (more specific in owo)
        win_phrases = [
            'you won', 'and you won', 'won **', '!!', 'congratulations',
            'victory', '🎉', '💰', '🏆', '✅'
        ]

        # Look for explicit loss indicators
        loss_phrases = [
            'lost it all', 'you lost', 'and you lost', 'lose', 'loser',
            'defeat', 'lost all', 'lost everything', ':c', '😭', '😢', '💸'
        ]

        # Check for win indicators first (owo uses "and you won **amount**!!")
        if any(phrase in content for phrase in win_phrases):
            return True
        elif any(phrase in content for phrase in loss_phrases):
            return False

        # If we can't determine, return None
        return None
    
    def _extract_amounts(self, content: str) -> Dict[str, Optional[int]]:
        """Extract monetary amounts from the message"""
        amounts = {}
        
        # Look for numbers that might be amounts
        number_matches = re.findall(r'\b(\d{1,3}(?:,\d{3})*|\d+)\b', content)
        
        amounts['numbers_found'] = [int(num.replace(',', '')) for num in number_matches]
        
        return amounts
    
    def _extract_sides(self, content: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract chosen side and result side"""
        side_chosen = None
        side_result = None

        # Look for "chose heads/tails" pattern first
        if 'chose **heads**' in content or 'chose heads' in content:
            side_chosen = 'heads'
        elif 'chose **tails**' in content or 'chose tails' in content:
            side_chosen = 'tails'

        # Look for result indicators in "The coin spins..." section
        # owo uses <:head:...> and <:tail:...> emojis for the result
        if '<:head:' in content:
            side_result = 'heads'
        elif '<:tail:' in content:
            side_result = 'tails'

        return side_chosen, side_result
    
    def _extract_balance(self, content: str) -> Optional[int]:
        """Extract new balance from the message"""
        # Look for balance-related patterns
        balance_keywords = ['balance', 'wallet', 'total', 'bank']
        
        for keyword in balance_keywords:
            if keyword in content:
                # Look for number after the keyword
                pattern = rf'{keyword}.*?(\d{{1,3}}(?:,\d{{3}})*|\d+)'
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return int(match.group(1).replace(',', ''))
        
        return None
    
    def debug_parse(self, message_content: str) -> Dict[str, Any]:
        """Debug function to see what the parser extracts"""
        content = message_content.lower()

        # Detailed analysis
        chosen_side, result_side = self._extract_sides(message_content)
        win_loss = self._determine_win_loss(content)

        # Check for logical inconsistency
        logical_win = None
        if chosen_side and result_side:
            logical_win = (chosen_side == result_side)

        return {
            'original_content': message_content,
            'is_coinflip': self.is_coinflip_response(content),
            'win_loss_detected': win_loss,
            'logical_win': logical_win,
            'chosen_side': chosen_side,
            'result_side': result_side,
            'amounts': self._extract_amounts(content),
            'balance': self._extract_balance(content),
            'inconsistency': logical_win is not None and win_loss is not None and logical_win != win_loss
        }
