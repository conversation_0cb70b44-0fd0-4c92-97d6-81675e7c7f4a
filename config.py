import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Discord Configuration
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN', 'MTAwNjY0OTgxODI0MTg0MzIwMA.Gq-QWd.VYW4EHKL2H5hVcmWFQoDraRKsvVSycKATMMF18')
    CHANNEL_ID = os.getenv('CHANNEL_ID', '1035420448013426739')

    # User Configuration
    USERNAME_IN_SERVER = os.getenv('USERNAME_IN_SERVER', 'NG PENZ')  # Your display name in the server
    
    # Discord Headers
    HEADERS = {
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'Authorization': DISCORD_TOKEN,
        'X-Super-Properties': 'eyJvcyI6IkxpbnV4IiwiYnJvd3NlciI6IkZpcmVmb3giLCJkZXZpY2UiOiIiLCJzeXN0ZW1fbG9jYWxlIjoiZW4tVVMiLCJoYXNfY2xpZW50X21vZHMiOmZhbHNlLCJicm93c2VyX3VzZXJfYWdlbnQiOiJNb3ppbGxhLzUuMCAoWDExOyBMaW51eCB4ODZfNjQ7IHJ2OjEzOS4wKSBHZWNrby8yMDEwMDEwMSBGaXJlZm94LzEzOS4wIiwiYnJvd3Nlcl92ZXJzaW9uIjoiMTM5LjAiLCJvc192ZXJzaW9uIjoiIiwicmVmZXJyZXIiOiJodHRwczovL3d3dy5nb29nbGUuY29tLyIsInJlZmVycmluZ19kb21haW4iOiJ3d3cuZ29vZ2xlLmNvbSIsInNlYXJjaF9lbmdpbmUiOiJnb29nbGUiLCJyZWZlcnJlcl9jdXJyZW50IjoiIiwicmVmZXJyaW5nX2RvbWFpbl9jdXJyZW50IjoiIiwicmVsZWFzZV9jaGFubmVsIjoic3RhYmxlIiwiY2xpZW50X2J1aWxkX251bWJlciI6NDA1MjA5LCJjbGllbnRfZXZlbnRfc291cmNlIjpudWxsLCJjbGllbnRfbGF1bmNoX2lkIjoiYTgwYjczZmYtODQxMC00NmNmLTg2YjgtYTdlNDE3ZjI2MmRkIiwiY2xpZW50X2hlYXJ0YmVhdF9zZXNzaW9uX2lkIjoiMTZkNzMyMzEtN2Y3NC00OWIzLWJiZmEtZjA5MThiNGVmOGVkIiwiY2xpZW50X2FwcF9zdGF0ZSI6ImZvY3VzZWQifQ==',
        'X-Discord-Locale': 'en-GB',
        'X-Discord-Timezone': 'Asia/Kolkata',
        'X-Debug-Options': 'bugReporterEnabled',
        'Origin': 'https://discord.com',
        'Alt-Used': 'discord.com',
        'Connection': 'keep-alive',
        'Referer': f'https://discord.com/channels/781430429177741342/{CHANNEL_ID}',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    # Betting Configuration
    DEFAULT_BET_AMOUNT = 100
    MAX_BET_AMOUNT = 10000
    MIN_BET_AMOUNT = 1
    
    # Risk Management
    MAX_DAILY_LOSS = 5000
    MAX_CONSECUTIVE_LOSSES = 10  # Stop after 10 consecutive losses
    STOP_LOSS_PERCENTAGE = 0.3  # Stop if lose 30% of bankroll
    MIN_BANKROLL_THRESHOLD = 1000  # Stop if bankroll goes below this
    
    # Rate Limiting
    MIN_BET_INTERVAL = 9   # Minimum seconds between bets
    MAX_BET_INTERVAL = 14  # Maximum seconds between bets
    BET_INTERVAL_RANGE = (9, 14)  # Random interval range to avoid owo bot timeouts
    
    # Strategy Settings
    MARTINGALE_MULTIPLIER = 2.0
    ARBITRAGE_MIN_EDGE = 0.02  # 2% minimum edge for arbitrage
