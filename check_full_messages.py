#!/usr/bin/env python3
"""
Check full owo bot messages to debug parsing
"""

from discord_client import DiscordClient

def main():
    client = DiscordClient()
    
    print("🔍 Fetching recent messages to check owo responses...")
    messages = client.get_recent_messages(limit=10)
    
    if not messages:
        print("❌ Failed to fetch messages")
        return
    
    print(f"\n📥 Found {len(messages)} messages")
    print("=" * 80)
    
    for i, msg in enumerate(messages):
        author = msg.get('author', {})
        author_name = author.get('username', 'Unknown')
        content = msg.get('content', '')
        
        if author_name.lower() == 'owo' and ('spent' in content.lower() or 'cf' in content.lower()):
            print(f"\n🎰 OWO BOT MESSAGE #{i+1}:")
            print(f"Author: {author_name}")
            print(f"FULL CONTENT:")
            print(f"'{content}'")
            print("-" * 80)

if __name__ == "__main__":
    main()
