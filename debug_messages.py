#!/usr/bin/env python3
"""
Debug tool to inspect Discord messages and test response parsing
"""

import json
from discord_client import DiscordClient
from owo_response_parser import OwoResponseParser

def main():
    client = DiscordClient()
    parser = OwoResponseParser()
    
    print("🔍 Discord Message Debug Tool")
    print("=" * 50)
    
    # Fetch recent messages
    messages = client.get_recent_messages(limit=20)
    
    if not messages:
        print("❌ Failed to fetch messages")
        return
    
    print(f"\n📥 Found {len(messages)} messages:")
    print("-" * 50)
    
    for i, msg in enumerate(messages):
        author = msg.get('author', {})
        author_name = author.get('username', 'Unknown')
        author_id = author.get('id', 'Unknown')
        content = msg.get('content', '')
        timestamp = msg.get('timestamp', '')
        
        print(f"\n{i+1}. Author: {author_name} (ID: {author_id})")
        print(f"   Time: {timestamp}")
        print(f"   Content: {content}")
        
        # Test if this looks like an owo response
        if parser.is_coinflip_response(content):
            print("   🎰 DETECTED: Looks like coinflip response!")
            debug_info = parser.debug_parse(content)
            print(f"   Debug: {json.dumps(debug_info, indent=6)}")
        
        print("-" * 30)
    
    print("\n🔍 Looking for owo bot specifically...")
    owo_messages = [msg for msg in messages if msg.get('author', {}).get('username', '').lower() == 'owo']
    
    if owo_messages:
        print(f"Found {len(owo_messages)} messages from OwO bot:")
        for msg in owo_messages:
            content = msg.get('content', '')
            print(f"  - {content[:100]}...")
    else:
        print("No messages found from 'OwO' username")
        
        # Check for other possible owo bot names
        possible_owo = [msg for msg in messages if 'owo' in msg.get('author', {}).get('username', '').lower()]
        if possible_owo:
            print("Found messages from users with 'owo' in name:")
            for msg in possible_owo:
                author = msg.get('author', {}).get('username', 'Unknown')
                content = msg.get('content', '')
                print(f"  - {author}: {content[:100]}...")

if __name__ == "__main__":
    main()
