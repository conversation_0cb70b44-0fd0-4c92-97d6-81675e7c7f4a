import json
import time
from typing import List, Dict, Any, Optional
from dataclasses import asdict
from betting_strategies.base_strategy import BetInfo, BetResult

class BetTracker:
    """Track and persist betting history and statistics"""
    
    def __init__(self, filename: str = "bet_history.json"):
        self.filename = filename
        self.pending_bets: List[BetInfo] = []
        self.completed_bets: List[BetInfo] = []
        self.load_history()
    
    def add_pending_bet(self, bet_info: BetInfo):
        """Add a bet that's waiting for results"""
        self.pending_bets.append(bet_info)
        self.save_history()
        print(f"📝 Added pending bet: {bet_info.amount} ({bet_info.side or 'random'})")
    
    def complete_bet(self, bet_info: BetInfo, won: bool, profit: int):
        """Mark a bet as completed with results"""
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        
        # Move from pending to completed
        if bet_info in self.pending_bets:
            self.pending_bets.remove(bet_info)
        
        self.completed_bets.append(bet_info)
        self.save_history()
        
        result_emoji = "✅" if won else "❌"
        print(f"{result_emoji} Bet completed: {bet_info.amount} -> {profit:+d}")
    
    def get_pending_bets(self) -> List[BetInfo]:
        """Get all bets waiting for results"""
        return self.pending_bets.copy()
    
    def get_recent_bets(self, count: int = 10) -> List[BetInfo]:
        """Get most recent completed bets"""
        return self.completed_bets[-count:] if self.completed_bets else []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Calculate comprehensive betting statistics"""
        if not self.completed_bets:
            return {
                'total_bets': 0,
                'total_wins': 0,
                'total_losses': 0,
                'win_rate': 0,
                'total_profit': 0,
                'total_wagered': 0,
                'roi': 0,
                'pending_bets': len(self.pending_bets)
            }
        
        total_bets = len(self.completed_bets)
        wins = sum(1 for bet in self.completed_bets if bet.result == BetResult.WIN)
        losses = total_bets - wins
        win_rate = wins / total_bets if total_bets > 0 else 0
        
        total_profit = sum(bet.profit for bet in self.completed_bets)
        total_wagered = sum(bet.amount for bet in self.completed_bets)
        roi = total_profit / total_wagered if total_wagered > 0 else 0
        
        return {
            'total_bets': total_bets,
            'total_wins': wins,
            'total_losses': losses,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'total_wagered': total_wagered,
            'roi': roi,
            'pending_bets': len(self.pending_bets),
            'average_bet': total_wagered / total_bets if total_bets > 0 else 0,
            'average_profit_per_bet': total_profit / total_bets if total_bets > 0 else 0
        }
    
    def save_history(self):
        """Save betting history to file"""
        try:
            data = {
                'pending_bets': [self._bet_to_dict(bet) for bet in self.pending_bets],
                'completed_bets': [self._bet_to_dict(bet) for bet in self.completed_bets],
                'last_updated': time.time()
            }
            
            with open(self.filename, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            print(f"⚠️ Failed to save bet history: {e}")
    
    def load_history(self):
        """Load betting history from file"""
        try:
            with open(self.filename, 'r') as f:
                data = json.load(f)
            
            self.pending_bets = [self._dict_to_bet(bet_dict) for bet_dict in data.get('pending_bets', [])]
            self.completed_bets = [self._dict_to_bet(bet_dict) for bet_dict in data.get('completed_bets', [])]
            
            print(f"📊 Loaded {len(self.completed_bets)} completed bets, {len(self.pending_bets)} pending")
            
        except FileNotFoundError:
            print("📊 No existing bet history found, starting fresh")
        except Exception as e:
            print(f"⚠️ Failed to load bet history: {e}")
    
    def _bet_to_dict(self, bet: BetInfo) -> Dict[str, Any]:
        """Convert BetInfo to dictionary for JSON serialization"""
        bet_dict = asdict(bet)
        bet_dict['result'] = bet.result.value if bet.result else None
        return bet_dict
    
    def _dict_to_bet(self, bet_dict: Dict[str, Any]) -> BetInfo:
        """Convert dictionary to BetInfo object"""
        result_str = bet_dict.pop('result', None)
        result = BetResult(result_str) if result_str else BetResult.PENDING
        
        bet = BetInfo(**bet_dict)
        bet.result = result
        return bet
