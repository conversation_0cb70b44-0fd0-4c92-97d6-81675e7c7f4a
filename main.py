#!/usr/bin/env python3
"""
Automated Betting System for owo Discord Bot
"""

import time
import sys
import random
from typing import Optional
from colorama import init, Fore, Style

from discord_client import DiscordClient
from bet_tracker import BetTracker
from betting_strategies.fixed_amount import FixedAmountStrategy
from betting_strategies.martingale import MartingaleStrategy
from betting_strategies.arbitrage import ArbitrageStrategy
from betting_strategies.dynamic_adaptive import DynamicAdaptiveStrategy
from betting_strategies.progressive import ProgressiveStrategy
from betting_strategies.advanced_mathematical import AdvancedMathematicalStrategy
from betting_strategies.owo_pattern_analyzer import OwoPatternAnalyzer
from owo_response_parser import OwoResponseParser
from config import Config

# Initialize colorama for colored output
init()

class BettingBot:
    def __init__(self):
        self.discord_client = DiscordClient()
        self.bet_tracker = BetTracker()
        self.response_parser = OwoResponseParser()
        self.strategy = None
        self.config = Config()
        self.running = False
    
    def setup_strategy(self, strategy_type: str, **kwargs):
        """Setup the betting strategy"""
        if strategy_type == "fixed":
            bankroll = kwargs.get('bankroll', 1000)
            bet_amount = kwargs.get('bet_amount', 100)
            side = kwargs.get('side', None)
            self.strategy = FixedAmountStrategy(bankroll, bet_amount, side)
            
        elif strategy_type == "martingale":
            bankroll = kwargs.get('bankroll', 1000)
            base_bet = kwargs.get('base_bet', 100)
            side = kwargs.get('side', None)
            self.strategy = MartingaleStrategy(bankroll, base_bet, side=side)

        elif strategy_type == "arbitrage":
            bankroll = kwargs.get('bankroll', 1000)
            base_bet = kwargs.get('base_bet', 100)
            min_edge = kwargs.get('min_edge', 0.02)
            self.strategy = ArbitrageStrategy(bankroll, base_bet, min_edge)

        elif strategy_type == "dynamic":
            bankroll = kwargs.get('bankroll', 1000)
            base_percentage = kwargs.get('base_percentage', 0.02)
            self.strategy = DynamicAdaptiveStrategy(bankroll, base_percentage)

        elif strategy_type == "progressive":
            bankroll = kwargs.get('bankroll', 1000)
            base_bet = kwargs.get('base_bet', 100)
            progression_type = kwargs.get('progression_type', 'fibonacci')
            self.strategy = ProgressiveStrategy(bankroll, base_bet, progression_type)

        elif strategy_type == "mathematical":
            bankroll = kwargs.get('bankroll', 1000)
            max_bet_percentage = kwargs.get('max_bet_percentage', 0.02)
            self.strategy = AdvancedMathematicalStrategy(bankroll, max_bet_percentage)

        elif strategy_type == "owo_analyzer":
            bankroll = kwargs.get('bankroll', 1000)
            self.strategy = OwoPatternAnalyzer(bankroll)

        else:
            raise ValueError(f"Unknown strategy: {strategy_type}")
        
        print(f"{Fore.GREEN}✅ Strategy setup: {strategy_type}{Style.RESET_ALL}")
        self.print_strategy_stats()
    
    def print_strategy_stats(self):
        """Print current strategy statistics"""
        if not self.strategy:
            return

        stats = self.strategy.get_stats()
        print(f"\n{Fore.CYAN}📊 Strategy Statistics:{Style.RESET_ALL}")
        print(f"  Bankroll: {stats['current_bankroll']:,} (started with {stats['initial_bankroll']:,})")
        print(f"  Profit: {stats['profit']:+,}")
        print(f"  ROI: {stats['roi']:.2%}")
        print(f"  Bets: {stats['total_bets']} (W:{stats['total_wins']} L:{stats['total_losses']})")
        if stats['total_bets'] > 0:
            print(f"  Win Rate: {stats['win_rate']:.2%}")
        print(f"  Streak: {stats['consecutive_wins']} wins, {stats['consecutive_losses']} losses")

        # Show arbitrage-specific stats if using arbitrage strategy
        if hasattr(self.strategy, 'get_arbitrage_stats'):
            arb_stats = self.strategy.get_arbitrage_stats()
            arb_data = arb_stats.get('arbitrage_data', {})

            if isinstance(arb_data, dict):
                print(f"\n{Fore.YELLOW}🔍 Arbitrage Analysis:{Style.RESET_ALL}")
                print(f"  Recent Results: {arb_data.get('recent_results_count', 0)}")
                print(f"  Recent Win Rate: {arb_data.get('recent_win_rate', 0):.2%}")
                print(f"  Current Streak: {arb_data.get('current_streak', 'None')}")

                next_opp = arb_data.get('next_opportunity')
                if next_opp:
                    print(f"  Next Opportunity: {next_opp['type']} ({next_opp['confidence']:.1%} confidence)")
                    print(f"  Reason: {next_opp['reason']}")
                else:
                    print(f"  Next Opportunity: None found")
    
    def print_tracker_stats(self):
        """Print bet tracker statistics"""
        stats = self.bet_tracker.get_statistics()
        print(f"\n{Fore.YELLOW}📈 Betting History:{Style.RESET_ALL}")
        print(f"  Total Bets: {stats['total_bets']}")
        print(f"  Win Rate: {stats['win_rate']:.2%}")
        print(f"  Total Profit: {stats['total_profit']:+,}")
        print(f"  ROI: {stats['roi']:.2%}")
        print(f"  Pending: {stats['pending_bets']}")
    
    def check_bet_results(self):
        """Check for results of pending bets"""
        pending_bets = self.bet_tracker.get_pending_bets()
        if not pending_bets:
            return

        print(f"🔍 Checking results for {len(pending_bets)} pending bets...")

        # Fetch recent messages
        messages = self.discord_client.get_recent_messages(limit=20)
        if not messages:
            print("⚠️ Could not fetch messages")
            return

        for bet_info in pending_bets.copy():
            print(f"🔍 Looking for result of bet: {bet_info.amount} ({bet_info.side}) placed at {bet_info.timestamp}")

            # Look for owo bot response after this bet (specifically for your username)
            owo_response = self.discord_client.find_owo_response(messages, bet_info.timestamp, self.config.USERNAME_IN_SERVER)

            if owo_response:
                print(f"✅ Found owo response: {owo_response.get('content', '')[:100]}...")

                # Parse the response
                result = self.response_parser.parse_coinflip_result(
                    owo_response.get('content', ''),
                    bet_info.amount
                )

                if result:
                    # Store actual result in bet_info for advanced strategies
                    if hasattr(result, 'actual_result') and result.actual_result:
                        bet_info.actual_result = result.actual_result
                    elif hasattr(result, 'side_result') and result.side_result:
                        bet_info.actual_result = result.side_result

                    # Update bet tracker
                    self.bet_tracker.complete_bet(bet_info, result.won, result.profit)

                    # Update strategy if available
                    if self.strategy:
                        self.strategy.update_result(bet_info, result.won, result.profit)

                    print(f"📊 Result found: {'WIN' if result.won else 'LOSS'} - Profit: {result.profit:+d}")
                else:
                    print(f"⚠️ Could not parse owo response: {owo_response.get('content', '')[:100]}...")
            else:
                print(f"⚠️ No owo response found for bet placed at {bet_info.timestamp}")

    def place_single_bet(self) -> bool:
        """Place a single bet using the current strategy"""
        if not self.strategy:
            print(f"{Fore.RED}❌ No strategy configured{Style.RESET_ALL}")
            return False

        # Check if strategy wants to stop
        if self.strategy.should_stop_betting():
            print(f"{Fore.RED}🛑 Strategy says to stop betting{Style.RESET_ALL}")
            return False

        # Get next bet from strategy
        bet_info = self.strategy.calculate_next_bet()
        if not bet_info:
            print(f"{Fore.YELLOW}⚠️ Strategy returned no bet{Style.RESET_ALL}")
            return False

        # Place the bet
        success = self.discord_client.place_bet(bet_info.amount, bet_info.side)
        if success:
            self.bet_tracker.add_pending_bet(bet_info)
            print(f"{Fore.GREEN}✅ Bet placed: {bet_info.amount} ({bet_info.side or 'random'}){Style.RESET_ALL}")

            # Wait a moment then check for results
            response_wait = random.uniform(4, 7)  # Random wait for owo response
            print(f"⏳ Waiting {response_wait:.1f}s for owo bot response...")
            time.sleep(response_wait)
            self.check_bet_results()

            return True
        else:
            print(f"{Fore.RED}❌ Failed to place bet{Style.RESET_ALL}")
            return False
    
    def run_continuous(self, max_bets: Optional[int] = None):
        """Run the bot continuously with enhanced monitoring"""
        if not self.strategy:
            print(f"{Fore.RED}❌ No strategy configured{Style.RESET_ALL}")
            return

        self.running = True
        bet_count = 0
        session_start_bankroll = self.strategy.current_bankroll

        print(f"{Fore.GREEN}🚀 Starting continuous betting...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📊 Starting bankroll: {session_start_bankroll:,}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}⚠️ Will stop after 10 consecutive losses{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Press Ctrl+C to stop manually{Style.RESET_ALL}")

        try:
            while self.running:
                # Check stop conditions before placing bet
                if self.strategy.should_stop_betting():
                    print(f"{Fore.RED}🛑 Strategy says to stop betting{Style.RESET_ALL}")
                    break

                if max_bets and bet_count >= max_bets:
                    print(f"{Fore.YELLOW}🏁 Reached maximum bet limit: {max_bets}{Style.RESET_ALL}")
                    break

                # Check for 10 consecutive losses
                if self.strategy.consecutive_losses >= 10:
                    print(f"{Fore.RED}🛑 STOPPING: 10 consecutive losses reached!{Style.RESET_ALL}")
                    print(f"{Fore.RED}💸 This is a safety measure to prevent further losses{Style.RESET_ALL}")
                    break

                success = self.place_single_bet()
                if success:
                    bet_count += 1

                    # Print stats every 3 bets or after significant events
                    if (bet_count % 3 == 0 or
                        self.strategy.consecutive_losses >= 5 or
                        self.strategy.consecutive_wins >= 3):
                        self.print_strategy_stats()

                        # Show session progress
                        session_profit = self.strategy.current_bankroll - session_start_bankroll
                        print(f"{Fore.CYAN}📈 Session: {bet_count} bets, {session_profit:+,} profit{Style.RESET_ALL}")

                # Random wait time between bets to avoid owo timeouts
                base_wait = random.uniform(self.config.MIN_BET_INTERVAL, self.config.MAX_BET_INTERVAL)

                # Add extra wait time based on performance
                if self.strategy.consecutive_losses >= 5:
                    base_wait += random.uniform(2, 4)  # Wait longer after losses
                elif self.strategy.consecutive_losses >= 8:
                    base_wait += random.uniform(5, 8)  # Wait much longer after many losses

                print(f"⏳ Waiting {base_wait:.1f} seconds (random 9-14s + performance adjustment)...")
                time.sleep(base_wait)

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}🛑 Manual stop requested...{Style.RESET_ALL}")
        finally:
            self.running = False

            # Final session summary
            session_profit = self.strategy.current_bankroll - session_start_bankroll
            session_roi = session_profit / session_start_bankroll if session_start_bankroll > 0 else 0

            print(f"\n{Fore.CYAN}📊 SESSION SUMMARY:{Style.RESET_ALL}")
            print(f"  Bets placed: {bet_count}")
            print(f"  Starting bankroll: {session_start_bankroll:,}")
            print(f"  Ending bankroll: {self.strategy.current_bankroll:,}")
            print(f"  Session profit: {session_profit:+,}")
            print(f"  Session ROI: {session_roi:+.2%}")
            print(f"  Final streak: {self.strategy.consecutive_wins} wins, {self.strategy.consecutive_losses} losses")

            self.print_strategy_stats()
            self.print_tracker_stats()

def main():
    bot = BettingBot()
    
    print(f"{Fore.CYAN}🎰 Automated Betting System for owo{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✅ Message fetching implemented - automatic result tracking enabled{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚠️ Test message fetching first to ensure it works with your Discord setup{Style.RESET_ALL}\n")
    
    while True:
        print(f"\n{Fore.CYAN}Available commands:{Style.RESET_ALL}")
        print("1. Setup Fixed Amount Strategy")
        print("2. Setup Martingale Strategy")
        print("3. Setup Arbitrage Strategy")
        print("4. Setup Dynamic Adaptive Strategy")
        print("5. Setup Progressive Strategy")
        print(f"6. Setup Advanced Mathematical Strategy {Fore.GREEN}(BEST FOR WINNING){Style.RESET_ALL}")
        print(f"7. Setup Owo Pattern Analyzer {Fore.YELLOW}(SPECIALIZED){Style.RESET_ALL}")
        print("8. Place Single Bet")
        print("9. Run Continuous Betting")
        print("10. Show Statistics")
        print("11. Test Message Fetching")
        print("12. Check Pending Bet Results")
        print("13. Exit")
        
        choice = input(f"\n{Fore.GREEN}Enter choice (1-13): {Style.RESET_ALL}").strip()

        if choice == "1":
            bankroll = int(input("Enter bankroll: "))
            bet_amount = int(input("Enter bet amount: "))
            side = input("Enter side (h/t/enter for random): ").strip() or None
            bot.setup_strategy("fixed", bankroll=bankroll, bet_amount=bet_amount, side=side)

        elif choice == "2":
            bankroll = int(input("Enter bankroll: "))
            base_bet = int(input("Enter base bet: "))
            side = input("Enter side (h/t/enter for random): ").strip() or None
            bot.setup_strategy("martingale", bankroll=bankroll, base_bet=base_bet, side=side)

        elif choice == "3":
            bankroll = int(input("Enter bankroll: "))
            base_bet = int(input("Enter base bet: "))
            min_edge = float(input("Enter minimum edge (0.02 = 2%): ") or "0.02")
            bot.setup_strategy("arbitrage", bankroll=bankroll, base_bet=base_bet, min_edge=min_edge)

        elif choice == "4":
            # Dynamic Adaptive Strategy (RECOMMENDED)
            print(f"{Fore.YELLOW}🎯 Dynamic Adaptive Strategy - Automatically adjusts bet sizes{Style.RESET_ALL}")
            bankroll = int(input(f"Enter current bankroll (default: 16262): ") or "16262")
            base_percentage = float(input("Enter base bet percentage (0.02 = 2% of bankroll): ") or "0.02")
            bot.setup_strategy("dynamic", bankroll=bankroll, base_percentage=base_percentage)

        elif choice == "5":
            # Progressive Strategy
            print(f"{Fore.YELLOW}📈 Progressive Strategy - Increases bets after losses{Style.RESET_ALL}")
            bankroll = int(input(f"Enter current bankroll (default: 11360): ") or "11360")
            base_bet = int(input("Enter base bet amount: "))
            print("Progression types: fibonacci, dalembert, custom")
            progression_type = input("Enter progression type (default: fibonacci): ") or "fibonacci"
            bot.setup_strategy("progressive", bankroll=bankroll, base_bet=base_bet, progression_type=progression_type)

        elif choice == "6":
            # Advanced Mathematical Strategy
            print(f"{Fore.GREEN}🧠 Advanced Mathematical Strategy - Uses AI and probability theory{Style.RESET_ALL}")
            print(f"{Fore.GREEN}   Features: Pattern recognition, Markov chains, Kelly criterion, streak analysis{Style.RESET_ALL}")
            bankroll = int(input(f"Enter current bankroll (default: 11360): ") or "11360")
            max_bet_pct = float(input("Enter max bet percentage (0.02 = 2%): ") or "0.02")
            bot.setup_strategy("mathematical", bankroll=bankroll, max_bet_percentage=max_bet_pct)

        elif choice == "7":
            # Owo Pattern Analyzer
            print(f"{Fore.YELLOW}🎯 Owo Pattern Analyzer - Specialized for owo bot patterns{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}   Features: Time patterns, sequence analysis, statistical bias detection{Style.RESET_ALL}")
            bankroll = int(input(f"Enter current bankroll (default: 11360): ") or "11360")
            bot.setup_strategy("owo_analyzer", bankroll=bankroll)

        elif choice == "8":
            bot.place_single_bet()

        elif choice == "9":
            max_bets = input("Enter max bets (enter for unlimited): ").strip()
            max_bets = int(max_bets) if max_bets else None
            bot.run_continuous(max_bets)

        elif choice == "10":
            bot.print_strategy_stats()
            bot.print_tracker_stats()

        elif choice == "11":
            # Test message fetching
            print(f"{Fore.CYAN}🧪 Testing message fetching...{Style.RESET_ALL}")
            messages = bot.discord_client.get_recent_messages(limit=10)
            if messages:
                print(f"✅ Fetched {len(messages)} messages")
                for i, msg in enumerate(messages[:3]):  # Show first 3 messages
                    author = msg.get('author', {}).get('username', 'Unknown')
                    content = msg.get('content', '')[:100]
                    print(f"  {i+1}. {author}: {content}...")
            else:
                print("❌ Failed to fetch messages")

        elif choice == "12":
            # Check pending bet results
            bot.check_bet_results()

        elif choice == "13":
            print(f"{Fore.GREEN}👋 Goodbye!{Style.RESET_ALL}")
            sys.exit(0)
            
        else:
            print(f"{Fore.RED}❌ Invalid choice{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
